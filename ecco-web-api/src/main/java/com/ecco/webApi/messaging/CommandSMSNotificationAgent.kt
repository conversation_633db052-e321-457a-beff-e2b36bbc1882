package com.ecco.webApi.messaging

import com.ecco.webApi.evidence.CommandCreatedEvent

/**
 * A CommandCreatedEvent handler which can determine, from the view model and user preferences, what to communicate.
 * We want the view model because it can include before and after values (eg risk flag on, off)
 * Note: [WorkerAllocatedNotificationAgent] is similar. These could be merged
 */
interface CommandSMSNotificationAgent {

    fun communicateEvent(event: CommandCreatedEvent)
 }
