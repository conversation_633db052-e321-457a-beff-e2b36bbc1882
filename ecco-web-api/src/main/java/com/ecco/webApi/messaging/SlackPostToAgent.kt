package com.ecco.webApi.messaging

import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.bus.MessageBus
import com.ecco.security.repositories.ContactRepository
import com.ecco.webApi.evidence.CommandCreatedEvent
import com.ecco.webApi.evidence.EvidenceAssociatedContactCommandViewModel
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEvent
import org.springframework.stereotype.Component
import org.springframework.util.Assert
import org.springframework.web.client.RestTemplate
import java.util.concurrent.ExecutorService
import javax.annotation.PostConstruct

/**
 * A CommandCreatedEvent handler which will post events to a Slack webhook
 */
@Component
class SlackPostToAgent(
    private val serviceRecipientRepository: ServiceRecipientRepository,
    private val contactRepository: ContactRepository,
    private val singleThreadExecutor: ExecutorService,
    private val messageBus: MessageBus<ApplicationEvent>,
    private val objectMapper: ObjectMapper,
) {
    private val log = LoggerFactory.getLogger(javaClass)

    private val restTemplate = RestTemplate()

    @PostConstruct
    protected fun init() {
        messageBus.subscribe(CommandCreatedEvent::class.java) { singleThreadExecutor.submit { this.syncEvent(it) } }
    }

    private fun syncEvent(event: CommandCreatedEvent) {
        if (event.viewModel is EvidenceAssociatedContactCommandViewModel) {
            val cmd = event.viewModel
            val contact = contactRepository.findById(cmd.contactId).get()
            val payload =
                SlackWebHookRequest(
                    "#lone-worker-status",
                    """Updated status for ${contact.displayName}
  attending ${serviceRecipientRepository.findById(cmd.serviceRecipientId!!).get().displayName}
 Status: ${cmd.attendanceStatus.name.lowercase()}""",
                    "ecco-notifications",
                )
            invokeWebhook(payload)
        }
    }

    private fun invokeWebhook(payload: SlackWebHookRequest) {
        try {
            val request = objectMapper.writeValueAsString(payload)
            log.info("Sending webhook to Slack:\n{}", request)
            val result = restTemplate.postForEntity(
                "*****************************************************************************",
                request,
                String::class.java,
            )
            Assert.state(result.statusCode.is2xxSuccessful, "Webhook Slack failed")
        } catch (e: JsonProcessingException) {
            e.printStackTrace() // what can we do?
        }
    }
}