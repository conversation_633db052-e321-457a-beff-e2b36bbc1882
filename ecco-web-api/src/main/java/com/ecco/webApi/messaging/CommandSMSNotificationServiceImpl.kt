package com.ecco.webApi.messaging

import com.ecco.config.service.SoftwareFeatureService
import com.ecco.dom.ReferralServiceRecipient
import com.ecco.dom.commands.ReferralTaskUpdateCommand
import com.ecco.dom.commands.ServiceRecipientTaskCommand
import com.ecco.dom.contacts.Contact
import com.ecco.dom.incidents.IncidentServiceRecipient
import com.ecco.dom.repairs.RepairServiceRecipient
import com.ecco.dom.servicerecipients.BaseServiceRecipient
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.time.toJDK
import com.ecco.infrastructure.util.BeanPropertyPlaceholderResolver
import com.ecco.repositories.incidents.IncidentRepository
import com.ecco.security.repositories.ContactRepository
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService
import com.ecco.serviceConfig.service.ServiceTypeService
import com.ecco.webApi.contacts.ContactCommandParams
import com.ecco.webApi.evidence.CommandCreatedEvent
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import org.springframework.util.PropertyPlaceholderHelper
import java.time.format.DateTimeFormatter
import java.time.format.FormatStyle

/**
 * A CommandCreatedEvent handler which can determine, from the view model and user preferences, what to communicate.
 * We want the view model because it can include before and after values (eg risk flag on, off)
 * Note: [WorkerAllocatedNotificationAgent] is similar. These could be merged
 */
@Service
class CommandSMSNotificationServiceImpl(
    private val serviceRecipientRepository: ServiceRecipientRepository,
    private val contactRepository: ContactRepository,
    private val repositoryBasedServiceCategorisationService: RepositoryBasedServiceCategorisationService,
    private val serviceTypeService: ServiceTypeService,
    private val messageHandler: ContactMessageCommandHandler,
    private val softwareFeatureService: SoftwareFeatureService,
    private val incidentRepository: IncidentRepository,
) : CommandSMSNotificationAgent {
    @Suppress("unused")
    private class Params(
        val urlServletBase: String,
        val serviceRecipientId: Int,
        val serviceName: String,
        val projectName: String,
        val keyWorkerName: String,
        val assessmentDateTime: String,
    )

    companion object {
        private val log = LoggerFactory.getLogger(CommandSMSNotificationServiceImpl::class.java)
    }

    private val format = DateTimeFormatter.ofLocalizedDateTime(FormatStyle.MEDIUM)

    private val placeholderSubstitutor = PropertyPlaceholderHelper("{", "}")

    // see https://ishansoninitj.medium.com/using-spring-application-events-within-transactional-contexts-11b41e764aab
    // NB TransactionalEventListener by default applies AFTER_COMMIT, which is what we're using mbassador for
    @TransactionalEventListener
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun communicateEvent(event: CommandCreatedEvent) {
        // restrict what can be sent via smsTemplate as a lot of commands come here
        if (event.command is ServiceRecipientTaskCommand) {
            // We are unlikely to be interested in every audit (which could be multiple per save if evidence)
            // and could be user audits / and simply 'taskInstance', so a start is to limit to specific 'tasks'
            // items - see ServiceRecipientTaskCommandHandler.handleTaskInternal.
            // NB task completion would be best with another event to capture manually complete (audit) and auto-completing (no-audit - see canCompleteWorkflowTaskIfPossible)
            if (event.command is ReferralTaskUpdateCommand) {
                // there isn't a common 'operation' 'add' that can be searched on, so just have to be specific logic
                // such as interview as ReferralTaskAssessmentDateCommandViewModel, but for now
                // we just send info to those tasks configured
                // TODO may be good to send the audit itself - then client can see changes
                attemptSms(event)
            }
        }
    }

    private fun attemptSms(event: CommandCreatedEvent) {
        try {
            var referralSms = softwareFeatureService.featureEnabled("referralOverview.communication")
            var repairSms = softwareFeatureService.featureEnabled("repair.communication")
            // if none of them, don't load anything - just return
            if (!referralSms && !repairSms) return

            var cmd = event.command as ServiceRecipientTaskCommand
            val srId = cmd.serviceRecipientId
            val recipient = serviceRecipientRepository.findById(srId).orElseThrow()

            if (!referralSms && recipient.prefix == ReferralServiceRecipient.PREFIX) {
                return
            }
            if (!repairSms && recipient.prefix == RepairServiceRecipient.PREFIX) {
                return
            }

            sendSmsIfContactWithMobileNumberAndSmsTemplate(event.urlServletBase, cmd, recipient)
        } catch (e: Exception) {
            log.error("Send SMS failed with: " + e.message, e)
        }
    }

    private fun sendSmsIfContactWithMobileNumberAndSmsTemplate(
        urlServletBase: String,
        command: ServiceRecipientTaskCommand,
        recipient: BaseServiceRecipient,
    ) {
        var contact: Contact? = null

        if (recipient.prefix == IncidentServiceRecipient.PREFIX) {
            // assigned worker
            var i = incidentRepository.findIncidentByServiceRecipientId(recipient.id).get()
            contact = i.supportWorkerId?.let { contactRepository.findById(it).orElseThrow() }
        } else {
            // referral's client
            contact = recipient.contact?.let { contactRepository.findById(it.id).orElseThrow() }
        }

        if (contact == null || contact.mobileNumber == null) return

        // look up details since likely triggered by messageBus publishAfterTxEnd
        val serviceCat = repositoryBasedServiceCategorisationService.getServiceCategorisation(
            recipient.serviceAllocationId,
        )
        val service = repositoryBasedServiceCategorisationService.getService(serviceCat.serviceId)
        val project = if (serviceCat.projectId != null) {
            repositoryBasedServiceCategorisationService.getProject(serviceCat.projectId)
        } else {
            null
        }
        // use a service, as we need a new tx
        val type = serviceTypeService.findOneDto(recipient.loadConfigServiceTypeId())

        // its possible a task is not on the servicetype - such as hactQuestionnaire, so we just assume no sms
        // but we could do some global config, or put the hactQuestionnaire on the servicetype as hidden
        // TODO: rename to svcRecSmsTemplate
        val smsTemplate = type.getTaskDefByNameStrict(command.taskName)?.settings?.get("smsTemplate")
            ?: return

        val params = Params(
            urlServletBase,
            recipient.id,
            service.name,
            if (project != null) project.name else "",
            if (recipient is ReferralServiceRecipient && recipient.referral.supportWorker != null) {
                recipient.referral.supportWorker.displayName
            } else {
                "?"
            },
            if (recipient is ReferralServiceRecipient && recipient.referral.interviewDate != null) {
                recipient.referral.interviewDate.toJDK().format(format)
            } else {
                "?"
            },
        )

        val contactId = contact.id.toInt() // TODO: This should be stored as Int given we haven't discovered any aliens wanting support

        sendToContact(command, params, contactId, smsTemplate)
        // sendToKeyWorkerIfPossible(...)
    }

    private fun sendToContact(command: ServiceRecipientTaskCommand, params: Params, contactId: Int, smsTemplate: String) {
        val body = substitutePlaceholders(smsTemplate, params)
        log.info("Sending SMS: $body")
        val cmd = ContactMessageCommandDto(contactId, body)
        messageHandler.handleCommand(command.userId, ContactCommandParams(contactId), cmd)
    }

    /**
     * Substitutes placeholders with values.  e.g. {referral.referredService.name} or ${service.name}
     */
    private fun substitutePlaceholders(source: String, bean: Any?): String {
        if (bean == null) {
            return source
        }
        val resolver = BeanPropertyPlaceholderResolver(bean)
        return placeholderSubstitutor.replacePlaceholders(source, resolver)
    }
}