package com.ecco.webApi.messaging

import com.ecco.buildings.repositories.FixedContainerRepository
import com.ecco.config.repositories.ListDefinitionRepository
import com.ecco.config.service.SoftwareFeatureService
import com.ecco.config.service.TemplateService
import com.ecco.dom.Service
import com.ecco.dom.commands.ServiceRecipientEvidenceCommand
import com.ecco.dom.commands.ServiceRecipientTaskCommand
import com.ecco.dom.incidents.Incident
import com.ecco.dom.repairs.RepairServiceRecipient
import com.ecco.dom.servicerecipients.BaseServiceRecipient
import com.ecco.dom.servicerecipients.BaseServiceRecipientCommand
import com.ecco.dom.servicerecipients.ServiceRecipientCommand
import com.ecco.evidence.repositories.ServiceRecipientRepository
import com.ecco.infrastructure.config.web.ConvertersConfig
import com.ecco.messaging.EmailService
import com.ecco.repositories.incidents.IncidentRepository
import com.ecco.service.LinearWorkflowService
import com.ecco.serviceConfig.repositories.TaskDefinitionRepository
import com.ecco.serviceConfig.service.RepositoryBasedServiceCategorisationService
import com.ecco.serviceConfig.service.ServiceTypeService
import com.ecco.serviceConfig.viewModel.ServiceTypeViewModel
import com.ecco.servicerecipient.ServiceRecipientMessages
import com.ecco.webApi.controllers.CreateReferralCommandViewModel
import com.ecco.webApi.evidence.CommandCreatedEvent
import com.ecco.webApi.evidence.CommentCommandViewModel
import com.ecco.webApi.evidence.EvidenceFormSnapshotCommandViewModel
import com.ecco.webApi.incidents.CreateIncidentCommandViewModel
import com.ecco.webApi.repairs.CreateRepairCommandViewModel
import com.ecco.webApi.taskFlow.ReferralTaskExitCommandViewModel
import com.ecco.webApi.taskFlow.ReferralTaskStatusCommandViewModel
import com.ecco.webApi.taskFlow.ServiceRecipientTaskCommandViewModel
import com.ecco.workflow.WorkflowTask
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import org.springframework.util.StringUtils

/**
 * A CommandCreatedEvent handler which can determine, from the view model and user preferences, what to communicate.
 * We want the view model because it can include before and after values (eg risk flag on, off)
 * Note: [WorkerAllocatedNotificationAgent] is similar. These could be merged
 */
@org.springframework.stereotype.Service
class CommandEmailNotificationServiceImpl(
    private val serviceRecipientRepository: ServiceRecipientRepository,
    private val repositoryBasedServiceCategorisationService: RepositoryBasedServiceCategorisationService,
    private val serviceTypeService: ServiceTypeService,
    private val emailService: EmailService,
    private val templateService: TemplateService,
    private val softwareFeatureService: SoftwareFeatureService,
    private val incidentRepository: IncidentRepository,
    private val buildingRepository: FixedContainerRepository,
    private val listDefinitionRepository: ListDefinitionRepository,
    private val taskDefinitionRepository: TaskDefinitionRepository,
): CommandEmailNotificationAgent {
    @Suppress("unused")
    private class Params(
        val urlServletBase: String,
        val serviceRecipientId: Int,
        val serviceName: String,
        val projectName: String,
        val taskNameDisplay: String,
        val taskName: String
    )

    companion object {
        private val log = LoggerFactory.getLogger(CommandEmailNotificationServiceImpl::class.java)
        private val objectMapper: ObjectMapper = ConvertersConfig.getObjectMapper()
    }

    // see https://ishansoninitj.medium.com/using-spring-application-events-within-transactional-contexts-11b41e764aab
    // NB TransactionalEventListener by default applies AFTER_COMMIT, which is what we're using mbassador for
    @TransactionalEventListener
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun communicateEvent(event: CommandCreatedEvent) {
        if (softwareFeatureService.featureEnabled("ecco.email")) {
            // TODO delegate to some rules and user preferences
            // for now we just have this...
            handleNewReferralEventEmailManager(event)
            handleTaskEventEmailManager(event)
            //handleSafeguardingEventEmailManager(event);
            //handleCloseRequestEmailManager(event);
        }
    }

    fun handleTaskEventEmailManager(event: CommandCreatedEvent) {
        var taskName: String? = null
        var sr: BaseServiceRecipient? = null
        // we want to catch this command also, which for reasons in its javadoc, isn't a SRTaskCommand
        if (event.viewModel is EvidenceFormSnapshotCommandViewModel) {
            taskName = ServiceRecipientTaskCommand.translateTaskName(event.viewModel.taskName)
            sr = serviceRecipientRepository.findById(event.viewModel.serviceRecipientId).get()
        }
        // NB this doesn't extend SRTaskCommandViewModel, but when we pull it out of the database into 'CommentCommand' it does!
        if (event.viewModel is CommentCommandViewModel) {
            // NB taskName isn't required on the view model, but is typically supplied client side
            taskName = ServiceRecipientTaskCommand.translateTaskName(event.viewModel.taskName)
            sr = serviceRecipientRepository.findById(event.viewModel.serviceRecipientId).get()
        }
        if (event.viewModel is ServiceRecipientTaskCommandViewModel) {
            taskName = ServiceRecipientTaskCommand.translateTaskName(event.viewModel.taskName)
            sr = serviceRecipientRepository.findById(event.viewModel.serviceRecipientId).get()
            // if we are a taskInstance we need the original task
            if (event.viewModel is ReferralTaskStatusCommandViewModel) {
                if (event.viewModel.completed?.to != null) {
                    val taskDefId = LinearWorkflowService.getTaskDefId(WorkflowTask.Handle.fromString(event.viewModel.taskHandle));
                    taskName = taskDefinitionRepository.findById(taskDefId).get().name
                    // could do some specific logic here in terms of finding an appropriate email address
                    // or change the templateName to "TaskComplete"
                }
            }
        }

        if ((taskName != null) && (sr != null)) {
            val type = serviceTypeService.findOneDto(sr.loadConfigServiceTypeId());
            // not all commands may have a task - eg 'rotaVisit'
            val task = type.getTaskDefByName(ServiceRecipientTaskCommand.translateTaskName(taskName))
            val taskTrigger = task?.settings != null && ("y" == task.settings["triggerEmail"])
            if (!taskTrigger) {
                return;
            }

            val taskTriggerEmailTo = task.settings["triggerEmailTo"];
            val idStr = ServiceRecipientMessages.Messages.getIdentifierText(sr)
            val displayName = if (sr.prefix == RepairServiceRecipient.PREFIX) {
                sr.displayName
            } else {
                "" // DATA PROTECTION - don't expose client name
            }
            val subject = "ECCO Task for $idStr $displayName"

            // we want to add the repair's parent (renaming getClientDisplayName())
            // but the repair can't see the Building domain and the ServiceRecipientMessages is static
            // so we just add here for now
            /*if (sr.prefix == RepairServiceRecipient.PREFIX) {
                val repairSr = sr as RepairServiceRecipient
                val bldgId = repairSr.repair.buildingId;
                buildingRepository.findById(bldgId).ifPresent { bldg ->
                    val bldgStr = bldg.displayName
                    subject += " for $bldgStr"
                }
            }*/

            notifyServiceEmail(
                event.urlServletBase,
                (event.command as BaseServiceRecipientCommand).serviceRecipientId,
                sr,
                taskName,
                type,
                subject,
                "TaskNotify",
                taskTriggerEmailTo
            )
        }
    }

    fun handleNewReferralEventEmailManager(event: CommandCreatedEvent) {
        //if (event.viewModel.javaClass.isAssignableFrom(CreateServiceRecipientCommandViewModel.class)) {
        if (event.viewModel is CreateReferralCommandViewModel) {
            notifyServiceEmail(
                event.urlServletBase,
                (event.command as BaseServiceRecipientCommand).serviceRecipientId,
                null,
                "ECCO New Referral",
                "ReferralCreated"
            )
        }

        if (event.viewModel is CreateRepairCommandViewModel) {
            val srId = (event.command as BaseServiceRecipientCommand).serviceRecipientId
            val sr = serviceRecipientRepository.findById(srId).orElseThrow()

            val idStr = ServiceRecipientMessages.Messages.getIdentifierText(sr)
            val displayName = sr.displayName
            val subject = "ECCO New Repair $idStr $displayName"

            notifyServiceEmail(
                event.urlServletBase,
                srId,
                null,
                subject,
                "RepairCreated"
            )
        }

        if (event.viewModel is CreateIncidentCommandViewModel) {
            notifyServiceEmail(
                event.urlServletBase,
                (event.command as BaseServiceRecipientCommand).serviceRecipientId,
                null,
                "ECCO New Incident",
                "IncidentCreated"
            )
        }
    }

    private fun handleSafeguardingEventEmailManager(event: CommandCreatedEvent) {
        if (event.viewModel is CommentCommandViewModel) {
            val cmd = event.command as ServiceRecipientEvidenceCommand
            if ("needsAssessmentReduction".equals(cmd.taskName, ignoreCase = true)) {
                notifyServiceEmail(
                    event.urlServletBase,
                    cmd.serviceRecipientId,
                    null,
                    "ECCO Safeguarding Entry",
                    "SafeguardingEntry"
                )
            }
        }
    }

    private fun handleExitedRequestEmailManager(event: CommandCreatedEvent) {
        if (event.viewModel is ReferralTaskExitCommandViewModel) {
            val cmd = event.command as ServiceRecipientCommand
            val vm = event.viewModel
            if (vm.exitedDateChange!!.from == null && vm.exitedDateChange!!.to != null) {
                notifyServiceEmail(
                    event.urlServletBase,
                    cmd.serviceRecipientId,
                    null,
                    "ECCO Close Request Entry",
                    "CloseEntry"
                )
            }
        }
    }

    private fun notifyServiceEmail(
        urlServletBase: String,
        serviceRecipientId: Int,
        taskName: String?,
        subject: String,
        templateName: String,
    ) {
        notifyServiceEmail(urlServletBase, serviceRecipientId, null, taskName, null, subject, templateName, null)
    }

    private fun notifyServiceEmail(
        urlServletBase: String,
        serviceRecipientId: Int,
        sr: BaseServiceRecipient?,
        taskName: String?,
        config: ServiceTypeViewModel?,
        subject: String,
        templateName: String,
        overrideToAddress: String?
    ) {
        // look up details since likely triggered by messageBus publishAfterTxEnd
        val recipient = sr ?: serviceRecipientRepository.findById(serviceRecipientId).orElseThrow()
        val serviceCat = repositoryBasedServiceCategorisationService.getServiceCategorisation(recipient.serviceAllocationId);
        val service = repositoryBasedServiceCategorisationService.getService(serviceCat.serviceId);
        val project = if (serviceCat.projectId != null) repositoryBasedServiceCategorisationService.getProject(serviceCat.projectId) else null;

        val defaultEmailOnService = service.getParameterAsString(Service.PARAM_EMAIL)
        var emailAddrs = overrideToAddress ?: defaultEmailOnService

        // use a service, as we need a new tx
        // this gets the service/project allocated to?
        val type = config ?: serviceTypeService.findOneDto(recipient?.loadConfigServiceTypeId());

        var taskNameDisplay = ""
        // NB getTask.. no longer null-checked but the command has just fired from the task, so should be fine
        if (taskName != null) {
            taskNameDisplay = type.getTaskDefByNameStrict(taskName).name
        }

        if (taskName != null) {
            // need to be able to choose an address based on some logic
            // which needs to load the incident, find the category and determine the email address
            var calculatedEmail = this.calculateEmailFrom(serviceRecipientId, type.getTaskDefByNameStrict(taskName).settings["triggerEmailToCalc"]);
            if (calculatedEmail != null) {
                // add to overrideToAddress if it exists
                emailAddrs = if (emailAddrs != null) "$emailAddrs,$calculatedEmail" else calculatedEmail
            }
        }

        if (!StringUtils.hasText(emailAddrs)) {
            log.error("EMAIL NOT SENT - no email address configured")
            return
        }
        val params = Params(
            urlServletBase,
            serviceRecipientId,
            service.name,
            if (project != null) project.name else "",
            taskNameDisplay.orEmpty(),
            taskName.orEmpty()
        )
        val body = templateService.getPopulatedTemplateWithSubstitutions(templateName, params)
        if (body == null) {
            log.error("EMAIL NOT SENT - no body")
            return
        }
        emailService.sendMessage(emailAddrs, subject, body)
    }

    private fun calculateEmailFrom(serviceRecipientId: Int, calculatedEmailOnConfig: String?): String? {

        if (calculatedEmailOnConfig != null && calculatedEmailOnConfig.startsWith("incident.category.serious:")) {
            val i = incidentRepository.findIncidentByServiceRecipientId(serviceRecipientId).orElseThrow()
            val significant = Incident.isSignificant(i, listDefinitionRepository, objectMapper)
            if (significant != null && significant) {
                return calculatedEmailOnConfig.substring("incident.category.serious:".length)
            }
        }

        return null
    }

}