package com.ecco.webApi.rota

import com.ecco.dao.DemandScheduleRepository
import com.ecco.dom.agreements.ServiceAgreement
import com.ecco.infrastructure.rest.hateoas.ApiLinkTo
import com.ecco.repositories.contracts.ContractRepository
import com.ecco.rota.webApi.dto.AgreementResource
import org.springframework.hateoas.server.LinkBuilder
import org.springframework.hateoas.server.mvc.RepresentationModelAssemblerSupport
import org.springframework.hateoas.server.mvc.WebMvcLinkBuilder.methodOn

class AgreementResourceAssembler(
    private val contractRepository: ContractRepository,
    scheduleRepository: DemandScheduleRepository,
    private val withSchedules: Boolean,
) : RepresentationModelAssemblerSupport<ServiceAgreement, AgreementResource>(
    AgreementController::class.java,
    AgreementResource::class.java,
) {
    companion object {
        fun linkToAppointmentScheduleCollection(agreementId: Long): LinkBuilder =
            ApiLinkTo.linkToApi(methodOn(AgreementController::class.java).listAppointments(agreementId))
    }

    private val demandResourceAssembler = DemandResourceAssembler(scheduleRepository)

    override fun toModel(entity: ServiceAgreement): AgreementResource {
        val resource = AgreementResource(entity.serviceRecipientId)
        resource.agreementId = entity.id
        if (entity.contract != null) {
            resource.contractId = entity.contract!!.id.toLong()
            resource.contractName = entity.contract!!.name
        }
        resource.agreedHours = entity.agreedHours
        resource.start = entity.start
        resource.end = entity.end
        resource.parameters = entity.parameters
        if (withSchedules) {
            // Convert each appointment schedule
            resource.demandSchedules = entity.appointmentSchedules.map { demandResourceAssembler.toModel(it) }
        }
        resource.addScheduleLink(linkToAppointmentScheduleCollection(entity.id))
        return resource
    }

    fun fromResource(serviceRecipientId: Int?, command: ServiceAgreementCommandDto): ServiceAgreement {
        val entity = ServiceAgreement()
        entity.serviceRecipientId = serviceRecipientId
        entity.start = command.start.to
        entity.end = command.end?.to
        if (command.contractId != null) {
            val c = contractRepository.findOne(command.contractId)
            entity.contract = c
        }
        entity.agreedCharge = command.agreementCharge
        entity.agreedHours = command.agreementHours
        return entity
    }
}